# NextYa Migration Files
#
# This directory contains database migration files.
#
# Essential Commands:
#   npm run db:create "migration_name"  - Create new migration
#   npm run db:migrate                  - Run pending migrations
#   npm run db:rollback                 - Rollback last migration
#   npm run db:generate                 - Generate TypeScript types
#   npm run db:reset                    - Reset database (destroys all data)
#
# Docker commands:
#   ./docker.sh db:create "name"        - Create new migration
#   ./docker.sh db:migrate              - Run migrations
#   ./docker.sh db:rollback             - Rollback migration
#   ./docker.sh db:generate             - Generate types
#   ./docker.sh db:reset                - Reset database
